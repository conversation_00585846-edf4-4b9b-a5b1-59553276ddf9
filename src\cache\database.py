"""
SQLite caching system for FFXIV Friends List Viewer
"""
import sqlite3
import json
import time
from typing import Optional, List
from datetime import datetime, timedelta
from contextlib import contextmanager

from src.api.models import Character, Friend, FriendsList, SearchResult
from src.utils.config import Config

class CacheDatabase:
    """SQLite-based caching system"""
    
    def __init__(self):
        self.db_path = Config.get_cache_db_path()
        self.cache_duration = Config.CACHE_DURATION
        self._init_database()
    
    def _init_database(self):
        """Initialize the database schema"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Character cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS character_cache (
                    lodestone_id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    server TEXT NOT NULL,
                    data_center TEXT NOT NULL,
                    character_data TEXT NOT NULL,
                    cached_at REAL NOT NULL
                )
            ''')
            
            # Friends list cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS friends_cache (
                    lodestone_id INTEGER PRIMARY KEY,
                    character_data TEXT NOT NULL,
                    friends_data TEXT NOT NULL,
                    cached_at REAL NOT NULL,
                    FOREIGN KEY (lodestone_id) REFERENCES character_cache (lodestone_id)
                )
            ''')
            
            # Search results cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS search_cache (
                    search_key TEXT PRIMARY KEY,
                    results_data TEXT NOT NULL,
                    cached_at REAL NOT NULL
                )
            ''')
            
            conn.commit()
    
    @contextmanager
    def _get_connection(self):
        """Get a database connection with proper cleanup"""
        conn = sqlite3.connect(self.db_path)
        try:
            yield conn
        finally:
            conn.close()
    
    def _is_cache_valid(self, cached_at: float) -> bool:
        """Check if cached data is still valid"""
        return (time.time() - cached_at) < self.cache_duration
    
    def _serialize_character(self, character: Character) -> str:
        """Serialize character object to JSON"""
        return json.dumps({
            'lodestone_id': character.lodestone_id,
            'name': character.name,
            'server': character.server,
            'data_center': character.data_center,
            'avatar_url': character.avatar_url,
            'portrait_url': character.portrait_url,
            'title': character.title,
            'free_company': character.free_company,
            'grand_company': character.grand_company,
            'city_state': character.city_state,
            'guardian_deity': character.guardian_deity,
            'nameday': character.nameday,
            'race': character.race,
            'clan': character.clan,
            'gender': character.gender
        })
    
    def _deserialize_character(self, data: str) -> Character:
        """Deserialize JSON to character object"""
        char_dict = json.loads(data)
        return Character(**char_dict)
    
    def _serialize_friends(self, friends: List[Friend]) -> str:
        """Serialize friends list to JSON"""
        friends_data = []
        for friend in friends:
            friends_data.append({
                'lodestone_id': friend.lodestone_id,
                'name': friend.name,
                'server': friend.server,
                'avatar_url': friend.avatar_url
            })
        return json.dumps(friends_data)
    
    def _deserialize_friends(self, data: str) -> List[Friend]:
        """Deserialize JSON to friends list"""
        friends_data = json.loads(data)
        friends = []
        for friend_dict in friends_data:
            friends.append(Friend(**friend_dict))
        return friends
    
    def cache_character(self, character: Character):
        """Cache character data"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO character_cache 
                (lodestone_id, name, server, data_center, character_data, cached_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                character.lodestone_id,
                character.name,
                character.server,
                character.data_center,
                self._serialize_character(character),
                time.time()
            ))
            conn.commit()
    
    def get_cached_character(self, lodestone_id: int) -> Optional[Character]:
        """Get cached character data"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT character_data, cached_at FROM character_cache 
                WHERE lodestone_id = ?
            ''', (lodestone_id,))
            
            result = cursor.fetchone()
            if result and self._is_cache_valid(result[1]):
                return self._deserialize_character(result[0])
        
        return None

    def cache_friends_list(self, friends_list: FriendsList):
        """Cache friends list data"""
        # First cache the character
        self.cache_character(friends_list.character)

        # Then cache the friends list
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO friends_cache
                (lodestone_id, character_data, friends_data, cached_at)
                VALUES (?, ?, ?, ?)
            ''', (
                friends_list.character.lodestone_id,
                self._serialize_character(friends_list.character),
                self._serialize_friends(friends_list.friends),
                time.time()
            ))
            conn.commit()

    def get_cached_friends_list(self, lodestone_id: int) -> Optional[FriendsList]:
        """Get cached friends list data"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT character_data, friends_data, cached_at FROM friends_cache
                WHERE lodestone_id = ?
            ''', (lodestone_id,))

            result = cursor.fetchone()
            if result and self._is_cache_valid(result[2]):
                character = self._deserialize_character(result[0])
                friends = self._deserialize_friends(result[1])

                return FriendsList(
                    character=character,
                    friends=friends,
                    last_updated=datetime.fromtimestamp(result[2])
                )

        return None

    def cache_search_results(self, name: str, server: str, results: List[SearchResult]):
        """Cache search results"""
        search_key = f"{name.lower()}:{server.lower()}"

        results_data = []
        for result in results:
            results_data.append({
                'lodestone_id': result.lodestone_id,
                'name': result.name,
                'server': result.server,
                'data_center': result.data_center,
                'avatar_url': result.avatar_url
            })

        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO search_cache
                (search_key, results_data, cached_at)
                VALUES (?, ?, ?)
            ''', (
                search_key,
                json.dumps(results_data),
                time.time()
            ))
            conn.commit()

    def get_cached_search_results(self, name: str, server: str) -> Optional[List[SearchResult]]:
        """Get cached search results"""
        search_key = f"{name.lower()}:{server.lower()}"

        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT results_data, cached_at FROM search_cache
                WHERE search_key = ?
            ''', (search_key,))

            result = cursor.fetchone()
            if result and self._is_cache_valid(result[1]):
                results_data = json.loads(result[0])
                results = []
                for result_dict in results_data:
                    results.append(SearchResult(**result_dict))
                return results

        return None

    def clear_expired_cache(self):
        """Clear expired cache entries"""
        cutoff_time = time.time() - self.cache_duration

        with self._get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('DELETE FROM character_cache WHERE cached_at < ?', (cutoff_time,))
            cursor.execute('DELETE FROM friends_cache WHERE cached_at < ?', (cutoff_time,))
            cursor.execute('DELETE FROM search_cache WHERE cached_at < ?', (cutoff_time,))

            conn.commit()

    def clear_all_cache(self):
        """Clear all cache data"""
        with self._get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('DELETE FROM character_cache')
            cursor.execute('DELETE FROM friends_cache')
            cursor.execute('DELETE FROM search_cache')

            conn.commit()
