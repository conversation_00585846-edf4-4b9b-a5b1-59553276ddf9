"""
Character search interface for FFXIV Friends List Viewer
"""
import customtkinter as ctk
from typing import Callable, Optional
import threading

from ..utils.validators import Validators
from ..api.models import SearchResult

class SearchFrame(ctk.CTkFrame):
    """Frame containing character search functionality"""
    
    def __init__(self, parent, search_callback: Callable[[str, str], None]):
        super().__init__(parent)
        
        self.search_callback = search_callback
        self.is_searching = False
        
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the search interface"""
        # Title
        title_label = ctk.CTkLabel(
            self, 
            text="Character Search", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # Search form frame
        form_frame = ctk.CTkFrame(self)
        form_frame.pack(fill="x", padx=20, pady=10)
        
        # Character name input
        name_label = ctk.CTkLabel(form_frame, text="Character Name:")
        name_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.name_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="Enter character name (e.g., <PERSON>e)",
            width=300
        )
        self.name_entry.pack(padx=10, pady=(0, 10))
        self.name_entry.bind("<Return>", self._on_search_clicked)
        
        # Server selection
        server_label = ctk.CTkLabel(form_frame, text="Server:")
        server_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        self.server_combobox = ctk.CTkComboBox(
            form_frame,
            values=Validators.get_all_servers(),
            width=300
        )
        self.server_combobox.pack(padx=10, pady=(0, 10))
        self.server_combobox.set("Select server...")
        
        # Search button
        self.search_button = ctk.CTkButton(
            form_frame,
            text="Search Character",
            command=self._on_search_clicked,
            width=200,
            height=40
        )
        self.search_button.pack(pady=20)
        
        # Status label
        self.status_label = ctk.CTkLabel(
            self,
            text="Enter character name and server to search",
            text_color="gray"
        )
        self.status_label.pack(pady=10)
        
        # Progress bar (initially hidden)
        self.progress_bar = ctk.CTkProgressBar(self, width=300)
        self.progress_bar.pack(pady=10)
        self.progress_bar.pack_forget()  # Hide initially
    
    def _on_search_clicked(self, event=None):
        """Handle search button click"""
        if self.is_searching:
            return
        
        name = self.name_entry.get().strip()
        server = self.server_combobox.get()
        
        # Validate inputs
        if not name:
            self._show_error("Please enter a character name")
            return
        
        if server == "Select server..." or not server:
            self._show_error("Please select a server")
            return
        
        # Validate character name format
        name_valid, name_error = Validators.validate_character_name(name)
        if not name_valid:
            self._show_error(f"Invalid character name: {name_error}")
            return
        
        # Validate server
        server_valid, server_error = Validators.validate_server(server)
        if not server_valid:
            self._show_error(f"Invalid server: {server_error}")
            return
        
        # Start search
        self._start_search(name, server)
    
    def _start_search(self, name: str, server: str):
        """Start the search process"""
        self.is_searching = True
        self.search_button.configure(text="Searching...", state="disabled")
        self.progress_bar.pack(pady=10)
        self.progress_bar.start()
        self._show_status("Searching for character...", "blue")
        
        # Run search in background thread
        search_thread = threading.Thread(
            target=self._search_worker,
            args=(name, server),
            daemon=True
        )
        search_thread.start()
    
    def _search_worker(self, name: str, server: str):
        """Background worker for search operation"""
        try:
            self.search_callback(name, server)
        except Exception as e:
            # Handle errors in main thread
            self.after(0, lambda: self._search_error(str(e)))
    
    def _search_complete(self):
        """Called when search completes successfully"""
        self.is_searching = False
        self.search_button.configure(text="Search Character", state="normal")
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        self._show_status("Search completed", "green")
    
    def _search_error(self, error_message: str):
        """Called when search encounters an error"""
        self.is_searching = False
        self.search_button.configure(text="Search Character", state="normal")
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        self._show_error(error_message)
    
    def _show_status(self, message: str, color: str = "gray"):
        """Show status message"""
        self.status_label.configure(text=message, text_color=color)
    
    def _show_error(self, message: str):
        """Show error message"""
        self._show_status(f"Error: {message}", "red")
    
    def reset(self):
        """Reset the search form"""
        self.name_entry.delete(0, "end")
        self.server_combobox.set("Select server...")
        self._show_status("Enter character name and server to search", "gray")
        
        if self.is_searching:
            self.is_searching = False
            self.search_button.configure(text="Search Character", state="normal")
            self.progress_bar.stop()
            self.progress_bar.pack_forget()
    
    def get_search_params(self) -> tuple[str, str]:
        """Get current search parameters"""
        return self.name_entry.get().strip(), self.server_combobox.get()
    
    def set_search_params(self, name: str, server: str):
        """Set search parameters"""
        self.name_entry.delete(0, "end")
        self.name_entry.insert(0, name)
        self.server_combobox.set(server)
