#!/usr/bin/env python3
"""
Test runner for FFXIV Friends List Viewer
"""
import sys
import subprocess
from pathlib import Path

def run_tests():
    """Run all tests"""
    print("FFXIV Friends List Viewer - Test Runner")
    print("=" * 50)
    
    # Check if pytest is available
    try:
        import pytest
    except ImportError:
        print("Error: pytest not found. Please install it with:")
        print("  pip install pytest")
        return False
    
    # Run tests
    test_dir = Path("tests")
    if not test_dir.exists():
        print("Error: tests directory not found")
        return False
    
    print("Running tests...")
    
    # Run pytest with coverage if available
    try:
        import pytest_cov
        cmd = ["python", "-m", "pytest", "tests/", "-v", "--cov=src", "--cov-report=term-missing"]
    except ImportError:
        cmd = ["python", "-m", "pytest", "tests/", "-v"]
    
    try:
        result = subprocess.run(cmd, check=True)
        print("\nAll tests passed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\nTests failed with exit code: {e.returncode}")
        return False

def main():
    """Main test runner"""
    success = run_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
