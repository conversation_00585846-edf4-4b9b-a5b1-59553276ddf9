# Installation Guide

This guide will help you install and run the FFXIV Friends List Viewer on your system.

## System Requirements

- **Operating System**: Windows 10/11, macOS 10.14+, or Linux
- **Python**: 3.8 or higher (for running from source)
- **Memory**: At least 512 MB RAM
- **Storage**: 100 MB free space
- **Internet**: Required for API access

## Installation Methods

### Method 1: Binary Release (Recommended)

1. Go to the [Releases](https://github.com/your-username/ffxiv-friends-viewer/releases) page
2. Download the latest release for your operating system:
   - Windows: `FFXIV-Friends-Viewer-Windows.zip`
   - macOS: `FFXIV-Friends-Viewer-macOS.zip`
   - Linux: `FFXIV-Friends-Viewer-Linux.zip`
3. Extract the archive to a folder of your choice
4. Run the executable:
   - Windows: Double-click `FFXIV-Friends-Viewer.exe`
   - macOS: Double-click `FFXIV-Friends-Viewer.app`
   - Linux: Run `./FFXIV-Friends-Viewer` in terminal

### Method 2: From Source

#### Prerequisites

1. Install Python 3.8 or higher from [python.org](https://python.org)
2. Ensure `pip` is installed (usually comes with Python)

#### Installation Steps

1. **Clone or download the repository**:
   ```bash
   git clone https://github.com/your-username/ffxiv-friends-viewer.git
   cd ffxiv-friends-viewer
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python src/main.py
   ```

#### Alternative: Using pip

You can also install directly using pip:
```bash
pip install git+https://github.com/your-username/ffxiv-friends-viewer.git
ffxiv-friends-viewer
```

## Building from Source

If you want to create your own executable:

1. **Install build dependencies**:
   ```bash
   pip install pyinstaller
   ```

2. **Run the build script**:
   ```bash
   python build.py
   ```

3. **Find the executable** in the `distribution/` folder

## Troubleshooting

### Common Issues

#### "Module not found" errors
- Make sure all dependencies are installed: `pip install -r requirements.txt`
- Check that you're using Python 3.8 or higher: `python --version`

#### Application won't start
- Check that you have an internet connection (required for API access)
- Try running from command line to see error messages
- Check the log file in `logs/app.log` for detailed error information

#### "Character not found" errors
- Verify the character name is spelled correctly
- Make sure the server name is correct
- Check that the character exists on The Lodestone

#### Friends list shows as private
- The character's friends list privacy settings prevent access
- This is a limitation of the Lodestone API, not the application

### Performance Issues

#### Slow loading
- The application caches data to improve performance
- First-time searches may be slower
- Check your internet connection speed

#### High memory usage
- The application loads images for character avatars
- This is normal behavior for GUI applications

### Platform-Specific Issues

#### Windows
- If Windows Defender flags the executable, add an exception
- Some antivirus software may quarantine the application

#### macOS
- You may need to allow the application in System Preferences > Security & Privacy
- If you get "App can't be opened" error, try right-clicking and selecting "Open"

#### Linux
- Make sure you have the required GUI libraries installed
- On Ubuntu/Debian: `sudo apt-get install python3-tk`
- On CentOS/RHEL: `sudo yum install tkinter`

## Getting Help

If you encounter issues not covered here:

1. Check the [Issues](https://github.com/your-username/ffxiv-friends-viewer/issues) page
2. Create a new issue with:
   - Your operating system and version
   - Python version (if running from source)
   - Complete error message
   - Steps to reproduce the problem

## Uninstallation

### Binary Installation
- Simply delete the application folder

### Source Installation
- Delete the cloned repository folder
- Optionally remove installed packages: `pip uninstall -r requirements.txt`

### Cache Data
The application stores cache data in:
- Windows: `%APPDATA%/ffxiv-friends-viewer/cache/`
- macOS: `~/Library/Application Support/ffxiv-friends-viewer/cache/`
- Linux: `~/.local/share/ffxiv-friends-viewer/cache/`

You can safely delete this folder to clear all cached data.
