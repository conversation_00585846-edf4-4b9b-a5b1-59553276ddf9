"""
Input validation utilities for FFXIV Friends List Viewer
"""
import re
from typing import Optional, Tuple

class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass

class Validators:
    """Input validation utilities"""
    
    # FFXIV server list (data centers and servers)
    SERVERS = {
        "Aether": ["Adamantoise", "Cactuar", "Faerie", "Gilgamesh", "Jenova", "Midgardsormr", "Sargatanas", "Siren"],
        "Crystal": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Coeurl", "Diabolos", "Goblin", "Malboro", "Mateus", "Zalera"],
        "Dynamis": ["<PERSON><PERSON><PERSON><PERSON>", "Golem", "Halicarnassus", "Kraken", "<PERSON><PERSON>", "Marilit<PERSON>", "Rafflesia", "Seraph"],
        "Primal": ["Behemoth", "Excalibur", "Exodus", "Famfrit", "Hyperion", "Lam<PERSON>", "<PERSON>ath<PERSON>", "Ultros"],
        "Chaos": ["<PERSON><PERSON><PERSON>", "<PERSON>oi<PERSON>", "Moogle", "Omega", "<PERSON>", "<PERSON>gna<PERSON>", "<PERSON><PERSON>tarius", "<PERSON><PERSON><PERSON><PERSON>"],
        "Light": ["<PERSON>", "<PERSON><PERSON>", "Odin", "Phoenix", "<PERSON><PERSON>", "Shiva", "<PERSON><PERSON>", "<PERSON><PERSON>iark"],
        "Mat<PERSON>": ["Bismarck", "<PERSON>vana", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"],
        "El<PERSON><PERSON>": ["<PERSON>eg<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>bu<PERSON><PERSON>", "Garuda", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>uh", "<PERSON><PERSON><PERSON>", "<PERSON>phon", "<PERSON><PERSON>"],
        "<PERSON><PERSON>": ["Alexander", "Bahamut", "Durandal", "Fenrir", "Ifrit", "Ridill", "Tiamat", "Ultima"],
        "Mana": ["Anima", "Asura", "Chocobo", "Hades", "Ixion", "Masamune", "Pandaemonium", "Titan"],
        "Meteor": ["Belias", "Mandragora", "Shinryu", "Valefor", "Yojimbo", "Zeromus"]
    }
    
    @classmethod
    def get_all_servers(cls) -> list:
        """Get a flat list of all servers"""
        servers = []
        for dc_servers in cls.SERVERS.values():
            servers.extend(dc_servers)
        return sorted(servers)
    
    @classmethod
    def validate_character_name(cls, name: str) -> Tuple[bool, Optional[str]]:
        """
        Validate FFXIV character name
        
        Args:
            name: Character name to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not name or not name.strip():
            return False, "Character name cannot be empty"
        
        name = name.strip()
        
        # Check length (FFXIV names are 2-20 characters)
        if len(name) < 2:
            return False, "Character name must be at least 2 characters long"
        if len(name) > 20:
            return False, "Character name cannot exceed 20 characters"
        
        # Check for valid characters (letters, spaces, apostrophes, hyphens)
        if not re.match(r"^[a-zA-Z\s'\-]+$", name):
            return False, "Character name can only contain letters, spaces, apostrophes, and hyphens"
        
        # Check for proper format (First Last or First Middle Last)
        parts = name.split()
        if len(parts) < 2:
            return False, "Character name must have at least first and last name"
        if len(parts) > 3:
            return False, "Character name cannot have more than 3 parts"
        
        # Each part should start with capital letter
        for part in parts:
            if not part[0].isupper():
                return False, "Each part of the name should start with a capital letter"
        
        return True, None
    
    @classmethod
    def validate_server(cls, server: str) -> Tuple[bool, Optional[str]]:
        """
        Validate FFXIV server name
        
        Args:
            server: Server name to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not server or not server.strip():
            return False, "Server name cannot be empty"
        
        server = server.strip()
        all_servers = cls.get_all_servers()
        
        if server not in all_servers:
            return False, f"Invalid server name. Must be one of: {', '.join(all_servers)}"
        
        return True, None
    
    @classmethod
    def get_data_center_for_server(cls, server: str) -> Optional[str]:
        """Get the data center for a given server"""
        for dc, servers in cls.SERVERS.items():
            if server in servers:
                return dc
        return None
