# FFXIV Friends List Viewer

A desktop application for retrieving and displaying Final Fantasy XIV player friends lists with a clean, user-friendly GUI.

## Features

- Search for FFXIV characters by name and server
- Display character friends list information
- Clean, intuitive graphical user interface
- Caching for improved performance
- Cross-platform compatibility (Windows, macOS, Linux)

## Technical Stack

- **Language:** Python 3.8+
- **GUI Framework:** customtkinter (modern tkinter alternative)
- **API:** XIVAPI (Lodestone data scraper)
- **HTTP Client:** requests
- **Caching:** SQLite
- **Packaging:** PyInstaller

## Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐
│   GUI Layer     │    │  API Client  │    │   XIVAPI    │
│  (customtkinter)│◄──►│   (requests) │◄──►│ Lodestone   │
│                 │    │              │    │   Scraper   │
└─────────────────┘    └──────────────┘    └─────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────┐
│  Data Models    │    │    Cache     │
│   (Character,   │    │   (SQLite)   │
│   Friends)      │    │              │
└─────────────────┘    └──────────────┘
```

## Installation

### From Source
1. Clone the repository
2. Install Python 3.8 or higher
3. Install dependencies: `pip install -r requirements.txt`
4. Run the application: `python src/main.py`

### Binary Release
Download the latest release from the releases page and run the executable.

## Usage

1. Launch the application
2. Enter a character name and select their server
3. Click "Search" to find the character
4. View the character's friends list in the results panel

## Rate Limiting and Caching

- The application implements respectful rate limiting to avoid overwhelming the XIVAPI service
- Results are cached locally to improve performance and reduce API calls
- Cache expires after 24 hours to ensure data freshness

## Legal Disclaimer

This application uses publicly available data from The Lodestone (FFXIV's official community site) through the XIVAPI service. While this data is publicly accessible, users should be aware that:

- Third-party tools are technically prohibited by Square Enix's Terms of Service
- This application does not modify the game client or provide gameplay advantages
- Use at your own discretion and risk
- The developers are not responsible for any account actions taken by Square Enix
