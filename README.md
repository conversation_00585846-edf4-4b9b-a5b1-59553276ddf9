# FFXIV Friends List Extractor

A desktop application for extracting and displaying Final Fantasy XIV friends list data from the Lodestone.

## Features

- Extract friends list data from FFXIV Lodestone character profiles
- Modern, clean GUI built with PyQt6
- Search and filter friends list
- Export data to CSV/JSON formats
- Respects Lodestone rate limits and ToS

## Installation

1. Install Python 3.8 or higher
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Run the application:
   ```bash
   python main.py
   ```

2. Enter a character name and server
3. Click "Load Friends List" to extract data
4. Use search/filter options to browse friends
5. Export data using the Export menu

## Important Notes

- This application scrapes public Lodestone data only
- Respects rate limits to avoid being blocked
- Only works with characters that have public friends lists
- Requires internet connection to fetch data

## Legal Disclaimer

This tool only accesses publicly available data from the FFXIV Lodestone website. Users are responsible for complying with Square Enix's Terms of Service.
