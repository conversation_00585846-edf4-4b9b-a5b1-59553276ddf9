["tests/test_models.py::TestModels::test_character_creation", "tests/test_models.py::TestModels::test_friend_creation", "tests/test_models.py::TestModels::test_friends_list_creation", "tests/test_models.py::TestModels::test_search_result_creation", "tests/test_models.py::TestModels::test_search_result_to_character", "tests/test_validators.py::TestValidators::test_get_all_servers", "tests/test_validators.py::TestValidators::test_get_data_center_for_server", "tests/test_validators.py::TestValidators::test_validate_character_name_invalid", "tests/test_validators.py::TestValidators::test_validate_character_name_valid", "tests/test_validators.py::TestValidators::test_validate_server_invalid", "tests/test_validators.py::TestValidators::test_validate_server_valid"]