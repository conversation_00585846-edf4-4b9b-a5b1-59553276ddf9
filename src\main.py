#!/usr/bin/env python3
"""
FFXIV Friends List Viewer - Main Application Entry Point

A desktop application for retrieving and displaying Final Fantasy XIV 
player friends lists with a clean, user-friendly GUI.
"""
import sys
import os
import logging
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from gui.main_window import MainWindow
from utils.config import Config

def setup_logging():
    """Setup application logging"""
    log_dir = Config.BASE_DIR / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    try:
        import customtkinter
    except ImportError:
        missing_deps.append("customtkinter")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow")
    
    if missing_deps:
        print("Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install dependencies with:")
        print("  pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main application entry point"""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info(f"Starting {Config.APP_NAME} v{Config.APP_VERSION}")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # Ensure required directories exist
        Config.ensure_directories()
        
        # Create and run the application
        app = MainWindow()
        
        logger.info("Application initialized successfully")
        
        # Start the GUI
        app.run()
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}", exc_info=True)
        
        # Show error dialog if possible
        try:
            import customtkinter as ctk
            
            # Create error dialog
            error_window = ctk.CTk()
            error_window.title("Fatal Error")
            error_window.geometry("500x300")
            
            error_text = f"""
A fatal error occurred:

{str(e)}

Please check the log file for more details.
            """.strip()
            
            error_label = ctk.CTkLabel(
                error_window,
                text=error_text,
                justify="left",
                wraplength=450
            )
            error_label.pack(padx=20, pady=20, expand=True)
            
            close_button = ctk.CTkButton(
                error_window,
                text="Close",
                command=error_window.destroy
            )
            close_button.pack(pady=(0, 20))
            
            error_window.mainloop()
            
        except:
            # If GUI error dialog fails, just print to console
            print(f"Fatal error: {str(e)}")
        
        sys.exit(1)

if __name__ == "__main__":
    main()
