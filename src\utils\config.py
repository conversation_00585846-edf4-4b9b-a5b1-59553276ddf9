"""
Configuration management for FFXIV Friends List Viewer
"""
import os
from pathlib import Path

class Config:
    """Application configuration settings"""
    
    # API Configuration
    XIVAPI_BASE_URL = "https://xivapi.com"
    API_TIMEOUT = 30  # seconds
    RATE_LIMIT_DELAY = 1.0  # seconds between requests
    
    # Cache Configuration
    CACHE_DURATION = 24 * 60 * 60  # 24 hours in seconds
    
    # Application Configuration
    APP_NAME = "FFXIV Friends List Viewer"
    APP_VERSION = "1.0.0"
    
    # Paths
    BASE_DIR = Path(__file__).parent.parent.parent
    CACHE_DIR = BASE_DIR / "cache"
    
    # GUI Configuration
    WINDOW_WIDTH = 800
    WINDOW_HEIGHT = 600
    WINDOW_MIN_WIDTH = 600
    WINDOW_MIN_HEIGHT = 400
    
    # Theme
    APPEARANCE_MODE = "dark"  # "light", "dark", "system"
    COLOR_THEME = "blue"  # "blue", "green", "dark-blue"
    
    @classmethod
    def ensure_directories(cls):
        """Ensure required directories exist"""
        cls.CACHE_DIR.mkdir(exist_ok=True)
    
    @classmethod
    def get_cache_db_path(cls):
        """Get the path to the cache database"""
        cls.ensure_directories()
        return cls.CACHE_DIR / "cache.db"
