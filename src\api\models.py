"""
Data models for FFXIV Friends List Viewer
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime

@dataclass
class Character:
    """Represents an FFXIV character"""
    lodestone_id: int
    name: str
    server: str
    data_center: str
    avatar_url: Optional[str] = None
    portrait_url: Optional[str] = None
    title: Optional[str] = None
    free_company: Optional[str] = None
    grand_company: Optional[str] = None
    city_state: Optional[str] = None
    guardian_deity: Optional[str] = None
    nameday: Optional[str] = None
    race: Optional[str] = None
    clan: Optional[str] = None
    gender: Optional[str] = None
    
    def __str__(self):
        return f"{self.name} ({self.server})"

@dataclass
class Friend:
    """Represents a friend in a character's friends list"""
    lodestone_id: int
    name: str
    server: str
    avatar_url: Optional[str] = None
    
    def __str__(self):
        return f"{self.name} ({self.server})"

@dataclass
class FriendsList:
    """Represents a character's friends list"""
    character: Character
    friends: List[Friend]
    last_updated: datetime
    
    def __len__(self):
        return len(self.friends)
    
    def __iter__(self):
        return iter(self.friends)

@dataclass
class SearchResult:
    """Represents a character search result"""
    lodestone_id: int
    name: str
    server: str
    data_center: str
    avatar_url: Optional[str] = None
    
    def to_character(self) -> Character:
        """Convert search result to Character object"""
        return Character(
            lodestone_id=self.lodestone_id,
            name=self.name,
            server=self.server,
            data_center=self.data_center,
            avatar_url=self.avatar_url
        )

class APIError(Exception):
    """Base exception for API-related errors"""
    pass

class CharacterNotFoundError(APIError):
    """Raised when a character is not found"""
    pass

class FriendsListPrivateError(APIError):
    """Raised when a character's friends list is private"""
    pass

class RateLimitError(APIError):
    """Raised when API rate limit is exceeded"""
    pass

class APITimeoutError(APIError):
    """Raised when API request times out"""
    pass
