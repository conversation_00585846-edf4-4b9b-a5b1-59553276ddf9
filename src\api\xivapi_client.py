"""
XIVAPI client for retrieving FFXIV character and friends list data
"""
import requests
import time
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin, quote

from .models import (
    Character, Friend, FriendsList, SearchResult,
    APIError, CharacterNotFoundError, FriendsListPrivateError,
    RateLimitError, APITimeoutError
)
from ..utils.config import Config
from ..utils.validators import Validators

class XIVAPIClient:
    """Client for interacting with the XIVAPI service"""
    
    def __init__(self):
        self.base_url = Config.XIVAPI_BASE_URL
        self.timeout = Config.API_TIMEOUT
        self.rate_limit_delay = Config.RATE_LIMIT_DELAY
        self.last_request_time = 0
        
        # Setup session with headers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': f'{Config.APP_NAME}/{Config.APP_VERSION}',
            'Accept': 'application/json'
        })
    
    def _rate_limit(self):
        """Implement rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[Any, Any]:
        """
        Make a request to the XIVAPI
        
        Args:
            endpoint: API endpoint
            params: Query parameters
            
        Returns:
            JSON response data
            
        Raises:
            APIError: For various API-related errors
        """
        self._rate_limit()
        
        url = urljoin(self.base_url, endpoint)
        
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            
            if response.status_code == 404:
                raise CharacterNotFoundError("Character not found")
            elif response.status_code == 429:
                raise RateLimitError("Rate limit exceeded")
            elif response.status_code != 200:
                raise APIError(f"API request failed with status {response.status_code}")
            
            return response.json()
            
        except requests.exceptions.Timeout:
            raise APITimeoutError("Request timed out")
        except requests.exceptions.RequestException as e:
            raise APIError(f"Request failed: {str(e)}")
    
    def search_character(self, name: str, server: str) -> List[SearchResult]:
        """
        Search for characters by name and server
        
        Args:
            name: Character name
            server: Server name
            
        Returns:
            List of search results
            
        Raises:
            APIError: If search fails
        """
        # Validate inputs
        name_valid, name_error = Validators.validate_character_name(name)
        if not name_valid:
            raise APIError(f"Invalid character name: {name_error}")
        
        server_valid, server_error = Validators.validate_server(server)
        if not server_valid:
            raise APIError(f"Invalid server: {server_error}")
        
        params = {
            'name': name,
            'server': server
        }
        
        data = self._make_request('/character/search', params)
        
        results = []
        if 'Results' in data:
            for result in data['Results']:
                search_result = SearchResult(
                    lodestone_id=result['ID'],
                    name=result['Name'],
                    server=result['Server'],
                    data_center=Validators.get_data_center_for_server(result['Server']) or 'Unknown',
                    avatar_url=result.get('Avatar')
                )
                results.append(search_result)
        
        return results

    def get_character(self, lodestone_id: int) -> Character:
        """
        Get detailed character information

        Args:
            lodestone_id: Character's Lodestone ID

        Returns:
            Character object with detailed information

        Raises:
            APIError: If character retrieval fails
        """
        endpoint = f'/character/{lodestone_id}'
        data = self._make_request(endpoint)

        if 'Character' not in data:
            raise CharacterNotFoundError("Character data not found")

        char_data = data['Character']

        character = Character(
            lodestone_id=lodestone_id,
            name=char_data['Name'],
            server=char_data['Server'],
            data_center=Validators.get_data_center_for_server(char_data['Server']) or 'Unknown',
            avatar_url=char_data.get('Avatar'),
            portrait_url=char_data.get('Portrait'),
            title=char_data.get('Title', {}).get('Name') if char_data.get('Title') else None,
            free_company=char_data.get('FreeCompanyName'),
            grand_company=char_data.get('GrandCompany', {}).get('NameID') if char_data.get('GrandCompany') else None,
            city_state=char_data.get('Town', {}).get('Name') if char_data.get('Town') else None,
            guardian_deity=char_data.get('GuardianDeity', {}).get('Name') if char_data.get('GuardianDeity') else None,
            nameday=char_data.get('Nameday'),
            race=char_data.get('Race', {}).get('Name') if char_data.get('Race') else None,
            clan=char_data.get('Tribe', {}).get('Name') if char_data.get('Tribe') else None,
            gender=char_data.get('Gender', {}).get('Name') if char_data.get('Gender') else None
        )

        return character

    def get_friends_list(self, lodestone_id: int) -> FriendsList:
        """
        Get character's friends list

        Args:
            lodestone_id: Character's Lodestone ID

        Returns:
            FriendsList object containing character and friends data

        Raises:
            APIError: If friends list retrieval fails
            FriendsListPrivateError: If friends list is private
        """
        endpoint = f'/character/{lodestone_id}'
        params = {'data': 'FR'}  # FR = Friends

        data = self._make_request(endpoint, params)

        if 'Character' not in data:
            raise CharacterNotFoundError("Character data not found")

        # Get character info
        character = self.get_character(lodestone_id)

        # Get friends list
        friends = []
        if 'Friends' in data:
            for friend_data in data['Friends']:
                friend = Friend(
                    lodestone_id=friend_data['ID'],
                    name=friend_data['Name'],
                    server=friend_data['Server'],
                    avatar_url=friend_data.get('Avatar')
                )
                friends.append(friend)
        else:
            # Check if friends list is private or empty
            if 'FriendsError' in data:
                raise FriendsListPrivateError("Friends list is private or unavailable")

        from datetime import datetime
        friends_list = FriendsList(
            character=character,
            friends=friends,
            last_updated=datetime.now()
        )

        return friends_list
