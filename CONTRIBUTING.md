# Contributing to FFXIV Friends List Viewer

Thank you for your interest in contributing to the FFXIV Friends List Viewer! This document provides guidelines and information for contributors.

## Code of Conduct

By participating in this project, you agree to abide by our code of conduct:
- Be respectful and inclusive
- Focus on constructive feedback
- Help create a welcoming environment for all contributors

## How to Contribute

### Reporting Bugs

1. Check the [Issues](https://github.com/your-username/ffxiv-friends-viewer/issues) page to see if the bug has already been reported
2. If not, create a new issue with:
   - Clear, descriptive title
   - Steps to reproduce the bug
   - Expected vs actual behavior
   - Your system information (OS, Python version, etc.)
   - Screenshots if applicable

### Suggesting Features

1. Check existing issues for similar feature requests
2. Create a new issue with:
   - Clear description of the feature
   - Use case and benefits
   - Possible implementation approach (if you have ideas)

### Contributing Code

#### Setting Up Development Environment

1. **Fork the repository** on GitHub
2. **Clone your fork**:
   ```bash
   git clone https://github.com/your-username/ffxiv-friends-viewer.git
   cd ffxiv-friends-viewer
   ```
3. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
4. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   pip install pytest pytest-cov  # For testing
   ```

#### Development Workflow

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```
2. **Make your changes**
3. **Run tests**:
   ```bash
   python run_tests.py
   ```
4. **Test the application**:
   ```bash
   python src/main.py
   ```
5. **Commit your changes**:
   ```bash
   git add .
   git commit -m "Add: brief description of changes"
   ```
6. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```
7. **Create a Pull Request** on GitHub

#### Code Style Guidelines

- **Python Style**: Follow PEP 8
- **Docstrings**: Use Google-style docstrings
- **Type Hints**: Use type hints where appropriate
- **Comments**: Write clear, concise comments
- **Naming**: Use descriptive variable and function names

Example:
```python
def validate_character_name(name: str) -> Tuple[bool, Optional[str]]:
    """
    Validate FFXIV character name format.
    
    Args:
        name: Character name to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    # Implementation here
```

#### Testing

- Write tests for new functionality
- Ensure all existing tests pass
- Aim for good test coverage
- Test files should be in the `tests/` directory
- Use descriptive test names

#### Documentation

- Update README.md if needed
- Add docstrings to new functions and classes
- Update INSTALL.md for installation changes
- Comment complex code sections

## Project Structure

```
ffxiv-friends-viewer/
├── src/                    # Source code
│   ├── api/               # API integration
│   ├── cache/             # Caching system
│   ├── gui/               # User interface
│   ├── utils/             # Utilities
│   └── main.py            # Application entry point
├── tests/                 # Test files
├── docs/                  # Documentation
├── requirements.txt       # Dependencies
└── README.md             # Main documentation
```

## Areas for Contribution

### High Priority
- Bug fixes
- Performance improvements
- Better error handling
- UI/UX improvements

### Medium Priority
- Additional features (export, filtering, etc.)
- Code refactoring
- Documentation improvements
- Test coverage improvements

### Low Priority
- New themes/styling
- Additional API integrations
- Internationalization

## Technical Guidelines

### API Usage
- Respect XIVAPI rate limits
- Implement proper error handling
- Cache responses appropriately
- Follow Square Enix ToS guidelines

### GUI Development
- Use customtkinter for consistency
- Ensure responsive design
- Handle long-running operations in background threads
- Provide user feedback for all actions

### Database/Caching
- Use SQLite for local caching
- Implement proper cache expiration
- Handle database errors gracefully

## Legal Considerations

### Square Enix Terms of Service
- This application uses publicly available Lodestone data
- Third-party tools are technically prohibited by Square Enix ToS
- We do not modify the game client or provide gameplay advantages
- Users assume responsibility for ToS compliance

### Contributions
- By contributing, you agree your code will be licensed under the MIT License
- Ensure you have the right to contribute any code you submit
- Do not include copyrighted material without permission

## Getting Help

If you need help with development:

1. Check existing documentation
2. Look at similar implementations in the codebase
3. Ask questions in GitHub Issues
4. Join our community discussions

## Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes for significant contributions
- GitHub contributors page

Thank you for helping make FFXIV Friends List Viewer better!
