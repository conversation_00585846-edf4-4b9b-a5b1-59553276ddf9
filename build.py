#!/usr/bin/env python3
"""
Build script for FFXIV Friends List Viewer
Creates executable distributions using PyInstaller
"""
import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build():
    """Clean previous build artifacts"""
    print("Cleaning previous build artifacts...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"  Removed {dir_name}/")
    
    # Clean .pyc files
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".pyc"):
                os.remove(os.path.join(root, file))

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable...")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",  # Create a single executable file
        "--windowed",  # Don't show console window (Windows)
        "--name", "FFXIV-Friends-Viewer",
        "--icon", "assets/icon.ico" if os.path.exists("assets/icon.ico") else None,
        "--add-data", "src;src",  # Include source files
        "--hidden-import", "customtkinter",
        "--hidden-import", "PIL._tkinter_finder",
        "src/main.py"
    ]
    
    # Remove None values
    cmd = [arg for arg in cmd if arg is not None]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        print(f"Executable created: dist/FFXIV-Friends-Viewer.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def create_distribution():
    """Create distribution package"""
    print("Creating distribution package...")
    
    dist_dir = Path("distribution")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    # Copy executable
    exe_path = Path("dist/FFXIV-Friends-Viewer.exe")
    if exe_path.exists():
        shutil.copy2(exe_path, dist_dir / "FFXIV-Friends-Viewer.exe")
    
    # Copy documentation
    files_to_copy = ["README.md", "LICENSE"]
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir / file_name)
    
    print(f"Distribution package created in: {dist_dir}")

def main():
    """Main build process"""
    print("FFXIV Friends List Viewer - Build Script")
    print("=" * 50)
    
    # Check if PyInstaller is available
    try:
        subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: PyInstaller not found. Please install it with:")
        print("  pip install pyinstaller")
        sys.exit(1)
    
    # Clean previous builds
    clean_build()
    
    # Build executable
    if not build_executable():
        sys.exit(1)
    
    # Create distribution
    create_distribution()
    
    print("\nBuild completed successfully!")
    print("You can find the executable in the 'distribution' folder.")

if __name__ == "__main__":
    main()
