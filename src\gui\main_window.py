"""
Main application window for FFXIV Friends List Viewer
"""
import customtkinter as ctk
from typing import List
import threading

from .search_frame import <PERSON><PERSON>rame
from .friends_frame import Friends<PERSON>rame
from ..api.xivapi_client import XIVAPIClient
from ..api.models import <PERSON>Result, APIError, CharacterNotFoundError, FriendsListPrivateError
from ..cache.database import CacheDatabase
from ..utils.config import Config

class MainWindow(ctk.CTk):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.api_client = XIVAPIClient()
        self.cache_db = CacheDatabase()
        
        # Setup window
        self._setup_window()
        self._setup_ui()
        
        # Cleanup expired cache on startup
        threading.Thread(target=self.cache_db.clear_expired_cache, daemon=True).start()
    
    def _setup_window(self):
        """Setup main window properties"""
        self.title(Config.APP_NAME)
        self.geometry(f"{Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
        self.minsize(Config.WINDOW_MIN_WIDTH, Config.WINDOW_MIN_HEIGHT)
        
        # Set appearance
        ctk.set_appearance_mode(Config.APPEARANCE_MODE)
        ctk.set_default_color_theme(Config.COLOR_THEME)
        
        # Center window on screen
        self.center_window()
    
    def center_window(self):
        """Center the window on the screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def _setup_ui(self):
        """Setup the user interface"""
        # Create main container
        main_container = ctk.CTkFrame(self)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create paned window for resizable layout
        paned_window = ctk.CTkFrame(main_container)
        paned_window.pack(fill="both", expand=True)
        
        # Left panel - Search
        left_panel = ctk.CTkFrame(paned_window, width=350)
        left_panel.pack(side="left", fill="y", padx=(0, 5))
        left_panel.pack_propagate(False)
        
        # Right panel - Results
        right_panel = ctk.CTkFrame(paned_window)
        right_panel.pack(side="right", fill="both", expand=True, padx=(5, 0))
        
        # Create search frame
        self.search_frame = SearchFrame(left_panel, self._on_character_search)
        self.search_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create friends frame
        self.friends_frame = FriendsFrame(right_panel)
        self.friends_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create menu bar
        self._setup_menu()
        
        # Status bar
        self.status_bar = ctk.CTkLabel(
            self,
            text="Ready",
            anchor="w",
            height=25
        )
        self.status_bar.pack(side="bottom", fill="x", padx=10, pady=(0, 5))
    
    def _setup_menu(self):
        """Setup application menu (simplified for customtkinter)"""
        # Note: customtkinter doesn't have native menu support
        # We'll add menu functionality through buttons if needed
        pass
    
    def _on_character_search(self, name: str, server: str):
        """Handle character search request"""
        try:
            # Update status
            self._update_status("Searching for character...")
            
            # Check cache first
            cached_results = self.cache_db.get_cached_search_results(name, server)
            if cached_results:
                self._update_status("Found cached search results")
                search_results = cached_results
            else:
                # Search via API
                search_results = self.api_client.search_character(name, server)
                
                # Cache results
                self.cache_db.cache_search_results(name, server, search_results)
                self._update_status(f"Found {len(search_results)} character(s)")
            
            if not search_results:
                self._handle_search_error("No characters found with that name and server")
                return
            
            # For now, take the first result (exact match should be first)
            character_result = search_results[0]
            
            # Get friends list
            self._load_friends_list(character_result.lodestone_id)
            
        except APIError as e:
            self._handle_search_error(str(e))
        except Exception as e:
            self._handle_search_error(f"Unexpected error: {str(e)}")
    
    def _load_friends_list(self, lodestone_id: int):
        """Load friends list for a character"""
        try:
            self._update_status("Loading friends list...")
            
            # Check cache first
            cached_friends = self.cache_db.get_cached_friends_list(lodestone_id)
            if cached_friends:
                self._update_status("Loaded friends list from cache")
                self._display_friends_list(cached_friends)
                return
            
            # Load from API
            friends_list = self.api_client.get_friends_list(lodestone_id)
            
            # Cache the results
            self.cache_db.cache_friends_list(friends_list)
            
            # Display results
            self._display_friends_list(friends_list)
            self._update_status(f"Loaded {len(friends_list.friends)} friends")
            
        except FriendsListPrivateError:
            self._handle_search_error("This character's friends list is private")
        except CharacterNotFoundError:
            self._handle_search_error("Character not found")
        except APIError as e:
            self._handle_search_error(str(e))
        except Exception as e:
            self._handle_search_error(f"Unexpected error: {str(e)}")
    
    def _display_friends_list(self, friends_list):
        """Display friends list in the UI"""
        # Update UI in main thread
        self.after(0, lambda: self.friends_frame.display_friends_list(friends_list))
        self.after(0, lambda: self.search_frame._search_complete())
    
    def _handle_search_error(self, error_message: str):
        """Handle search errors"""
        self._update_status(f"Error: {error_message}")
        self.after(0, lambda: self.search_frame._search_error(error_message))
    
    def _update_status(self, message: str):
        """Update status bar message"""
        self.after(0, lambda: self.status_bar.configure(text=message))
    
    def _on_clear_cache(self):
        """Clear all cached data"""
        try:
            self.cache_db.clear_all_cache()
            self._update_status("Cache cleared successfully")
        except Exception as e:
            self._update_status(f"Error clearing cache: {str(e)}")
    
    def _on_about(self):
        """Show about dialog"""
        about_text = f"""
{Config.APP_NAME} v{Config.APP_VERSION}

A desktop application for viewing FFXIV character friends lists.

Uses data from The Lodestone via XIVAPI.

Legal Notice:
This application uses publicly available data from The Lodestone.
Third-party tools are technically prohibited by Square Enix's ToS.
Use at your own discretion and risk.
        """.strip()
        
        # Create about window
        about_window = ctk.CTkToplevel(self)
        about_window.title("About")
        about_window.geometry("400x300")
        about_window.resizable(False, False)
        
        # Center on parent
        about_window.transient(self)
        about_window.grab_set()
        
        # About text
        about_label = ctk.CTkLabel(
            about_window,
            text=about_text,
            justify="left",
            wraplength=350
        )
        about_label.pack(padx=20, pady=20, expand=True)
        
        # Close button
        close_button = ctk.CTkButton(
            about_window,
            text="Close",
            command=about_window.destroy
        )
        close_button.pack(pady=(0, 20))
    
    def run(self):
        """Start the application"""
        self.mainloop()
