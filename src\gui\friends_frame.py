"""
Friends list display interface for FFXIV Friends List Viewer
"""
import customtkinter as ctk
from typing import Optional, List
import threading
from PIL import Image
import requests
from io import BytesIO

from ..api.models import Character, Friend, FriendsList

class FriendsFrame(ctk.CTkScrollableFrame):
    """Frame for displaying character and friends list information"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.current_friends_list: Optional[FriendsList] = None
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the friends display interface"""
        # Title
        self.title_label = ctk.CTkLabel(
            self, 
            text="Friends List", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.title_label.pack(pady=(10, 20))
        
        # Character info frame (initially hidden)
        self.character_frame = ctk.CTkFrame(self)
        self.character_frame.pack(fill="x", padx=20, pady=10)
        self.character_frame.pack_forget()
        
        # Friends list frame (initially hidden)
        self.friends_list_frame = ctk.CTkFrame(self)
        self.friends_list_frame.pack(fill="both", expand=True, padx=20, pady=10)
        self.friends_list_frame.pack_forget()
        
        # Empty state
        self.empty_label = ctk.CTkLabel(
            self,
            text="Search for a character to view their friends list",
            text_color="gray",
            font=ctk.CTkFont(size=14)
        )
        self.empty_label.pack(expand=True)
    
    def _setup_character_info(self, character: Character):
        """Setup character information display"""
        # Clear existing content
        for widget in self.character_frame.winfo_children():
            widget.destroy()
        
        # Character info container
        info_container = ctk.CTkFrame(self.character_frame)
        info_container.pack(fill="x", padx=10, pady=10)
        
        # Avatar and basic info frame
        header_frame = ctk.CTkFrame(info_container)
        header_frame.pack(fill="x", padx=10, pady=10)
        
        # Avatar placeholder (will be loaded asynchronously)
        self.avatar_label = ctk.CTkLabel(
            header_frame,
            text="Loading...",
            width=64,
            height=64
        )
        self.avatar_label.pack(side="left", padx=(10, 20), pady=10)
        
        # Character details
        details_frame = ctk.CTkFrame(header_frame)
        details_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        
        # Name and server
        name_label = ctk.CTkLabel(
            details_frame,
            text=f"{character.name}",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        name_label.pack(anchor="w", padx=10, pady=(10, 5))
        
        server_label = ctk.CTkLabel(
            details_frame,
            text=f"{character.server} ({character.data_center})",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        server_label.pack(anchor="w", padx=10, pady=(0, 10))
        
        # Additional info if available
        if character.title:
            title_label = ctk.CTkLabel(
                details_frame,
                text=f"Title: {character.title}",
                font=ctk.CTkFont(size=12)
            )
            title_label.pack(anchor="w", padx=10, pady=2)
        
        if character.free_company:
            fc_label = ctk.CTkLabel(
                details_frame,
                text=f"Free Company: {character.free_company}",
                font=ctk.CTkFont(size=12)
            )
            fc_label.pack(anchor="w", padx=10, pady=2)
        
        if character.race and character.clan:
            race_label = ctk.CTkLabel(
                details_frame,
                text=f"Race: {character.race} ({character.clan})",
                font=ctk.CTkFont(size=12)
            )
            race_label.pack(anchor="w", padx=10, pady=2)
        
        # Load avatar asynchronously
        if character.avatar_url:
            self._load_avatar(character.avatar_url)
    
    def _load_avatar(self, avatar_url: str):
        """Load character avatar asynchronously"""
        def load_image():
            try:
                response = requests.get(avatar_url, timeout=10)
                response.raise_for_status()
                
                image = Image.open(BytesIO(response.content))
                image = image.resize((64, 64), Image.Resampling.LANCZOS)
                
                # Convert to CTkImage
                ctk_image = ctk.CTkImage(light_image=image, dark_image=image, size=(64, 64))
                
                # Update UI in main thread
                self.after(0, lambda: self.avatar_label.configure(image=ctk_image, text=""))
                
            except Exception:
                # If loading fails, show placeholder
                self.after(0, lambda: self.avatar_label.configure(text="No Image"))
        
        # Load in background thread
        threading.Thread(target=load_image, daemon=True).start()
    
    def _setup_friends_list(self, friends: List[Friend]):
        """Setup friends list display"""
        # Clear existing content
        for widget in self.friends_list_frame.winfo_children():
            widget.destroy()
        
        # Friends list header
        header_frame = ctk.CTkFrame(self.friends_list_frame)
        header_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        friends_count_label = ctk.CTkLabel(
            header_frame,
            text=f"Friends ({len(friends)})",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        friends_count_label.pack(side="left", padx=10, pady=10)
        
        # Search/filter frame
        filter_frame = ctk.CTkFrame(header_frame)
        filter_frame.pack(side="right", padx=10, pady=5)
        
        self.filter_entry = ctk.CTkEntry(
            filter_frame,
            placeholder_text="Filter friends...",
            width=200
        )
        self.filter_entry.pack(side="left", padx=5, pady=5)
        self.filter_entry.bind("<KeyRelease>", self._on_filter_changed)
        
        # Friends container
        self.friends_container = ctk.CTkScrollableFrame(self.friends_list_frame)
        self.friends_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Store original friends list for filtering
        self.all_friends = friends
        self._display_friends(friends)
    
    def _display_friends(self, friends: List[Friend]):
        """Display the friends list"""
        # Clear existing friends
        for widget in self.friends_container.winfo_children():
            widget.destroy()
        
        if not friends:
            no_friends_label = ctk.CTkLabel(
                self.friends_container,
                text="No friends found" if hasattr(self, 'filter_entry') and self.filter_entry.get() else "This character has no friends",
                text_color="gray"
            )
            no_friends_label.pack(pady=20)
            return
        
        # Display each friend
        for friend in friends:
            self._create_friend_item(friend)
    
    def _create_friend_item(self, friend: Friend):
        """Create a single friend item widget"""
        friend_frame = ctk.CTkFrame(self.friends_container)
        friend_frame.pack(fill="x", padx=5, pady=2)
        
        # Friend info
        info_frame = ctk.CTkFrame(friend_frame)
        info_frame.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        
        name_label = ctk.CTkLabel(
            info_frame,
            text=friend.name,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        name_label.pack(anchor="w", padx=10, pady=(5, 2))
        
        server_label = ctk.CTkLabel(
            info_frame,
            text=friend.server,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        server_label.pack(anchor="w", padx=10, pady=(0, 5))
    
    def _on_filter_changed(self, event=None):
        """Handle filter text change"""
        if not hasattr(self, 'all_friends'):
            return
        
        filter_text = self.filter_entry.get().lower()
        
        if not filter_text:
            filtered_friends = self.all_friends
        else:
            filtered_friends = [
                friend for friend in self.all_friends
                if filter_text in friend.name.lower() or filter_text in friend.server.lower()
            ]
        
        self._display_friends(filtered_friends)
    
    def display_friends_list(self, friends_list: FriendsList):
        """Display a complete friends list"""
        self.current_friends_list = friends_list
        
        # Hide empty state
        self.empty_label.pack_forget()
        
        # Setup character info
        self._setup_character_info(friends_list.character)
        self.character_frame.pack(fill="x", padx=20, pady=10)
        
        # Setup friends list
        self._setup_friends_list(friends_list.friends)
        self.friends_list_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Update title
        self.title_label.configure(text=f"Friends List - {friends_list.character.name}")
    
    def clear(self):
        """Clear the friends list display"""
        self.current_friends_list = None
        
        # Hide frames
        self.character_frame.pack_forget()
        self.friends_list_frame.pack_forget()
        
        # Show empty state
        self.empty_label.pack(expand=True)
        
        # Reset title
        self.title_label.configure(text="Friends List")
