"""
Tests for input validation utilities
"""
import pytest
import sys
from pathlib import Path

# Add src to path for testing
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from utils.validators import Validators

class TestValidators:
    """Test cases for the Validators class"""
    
    def test_validate_character_name_valid(self):
        """Test valid character names"""
        valid_names = [
            "<PERSON>",
            "<PERSON>",
            "A'lara <PERSON>",
            "O'raha Mia",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>"
        ]
        
        for name in valid_names:
            is_valid, error = Validators.validate_character_name(name)
            assert is_valid, f"'{name}' should be valid, but got error: {error}"
    
    def test_validate_character_name_invalid(self):
        """Test invalid character names"""
        invalid_names = [
            "",  # Empty
            " ",  # Just spaces
            "A",  # Too short
            "john",  # No capital
            "john doe",  # No capitals
            "<PERSON>",  # Only first name
            "<PERSON>",  # Too many parts
            "John123",  # Numbers
            "<PERSON>",  # Special characters
            "A" * 21,  # Too long
        ]
        
        for name in invalid_names:
            is_valid, error = Validators.validate_character_name(name)
            assert not is_valid, f"'{name}' should be invalid"
            assert error is not None, f"Error message should be provided for '{name}'"
    
    def test_validate_server_valid(self):
        """Test valid server names"""
        valid_servers = [
            "Gilgamesh",
            "Balmung",
            "Tonberry",
            "Odin"
        ]
        
        for server in valid_servers:
            is_valid, error = Validators.validate_server(server)
            assert is_valid, f"'{server}' should be valid, but got error: {error}"
    
    def test_validate_server_invalid(self):
        """Test invalid server names"""
        invalid_servers = [
            "",  # Empty
            " ",  # Just spaces
            "InvalidServer",  # Not in list
            "gilgamesh",  # Wrong case
        ]
        
        for server in invalid_servers:
            is_valid, error = Validators.validate_server(server)
            assert not is_valid, f"'{server}' should be invalid"
            assert error is not None, f"Error message should be provided for '{server}'"
    
    def test_get_all_servers(self):
        """Test getting all servers"""
        servers = Validators.get_all_servers()
        
        assert isinstance(servers, list)
        assert len(servers) > 0
        assert "Gilgamesh" in servers
        assert "Balmung" in servers
        assert "Tonberry" in servers
        
        # Should be sorted
        assert servers == sorted(servers)
    
    def test_get_data_center_for_server(self):
        """Test getting data center for server"""
        test_cases = [
            ("Gilgamesh", "Aether"),
            ("Balmung", "Crystal"),
            ("Tonberry", "Elemental"),
            ("Odin", "Light"),
            ("InvalidServer", None)
        ]
        
        for server, expected_dc in test_cases:
            dc = Validators.get_data_center_for_server(server)
            assert dc == expected_dc, f"Expected {expected_dc} for {server}, got {dc}"

if __name__ == "__main__":
    pytest.main([__file__])
