"""
Tests for data models
"""
import pytest
import sys
from pathlib import Path
from datetime import datetime

# Add src to path for testing
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from api.models import Character, Friend, FriendsList, SearchResult

class TestModels:
    """Test cases for data models"""
    
    def test_character_creation(self):
        """Test Character model creation"""
        character = Character(
            lodestone_id=12345,
            name="<PERSON>",
            server="<PERSON><PERSON><PERSON>",
            data_center="Aether",
            avatar_url="https://example.com/avatar.jpg"
        )
        
        assert character.lodestone_id == 12345
        assert character.name == "<PERSON>"
        assert character.server == "Gilgamesh"
        assert character.data_center == "Aether"
        assert character.avatar_url == "https://example.com/avatar.jpg"
        assert str(character) == "<PERSON> (<PERSON>sh)"
    
    def test_friend_creation(self):
        """Test Friend model creation"""
        friend = Friend(
            lodestone_id=67890,
            name="<PERSON>",
            server="<PERSON><PERSON><PERSON>",
            avatar_url="https://example.com/avatar2.jpg"
        )
        
        assert friend.lodestone_id == 67890
        assert friend.name == "<PERSON>"
        assert friend.server == "Balmung"
        assert friend.avatar_url == "https://example.com/avatar2.jpg"
        assert str(friend) == "Jane Smith (Balmung)"
    
    def test_friends_list_creation(self):
        """Test FriendsList model creation"""
        character = Character(
            lodestone_id=12345,
            name="John Doe",
            server="Gilgamesh",
            data_center="Aether"
        )
        
        friends = [
            Friend(lodestone_id=67890, name="Jane Smith", server="Balmung"),
            Friend(lodestone_id=11111, name="Bob Johnson", server="Gilgamesh")
        ]
        
        friends_list = FriendsList(
            character=character,
            friends=friends,
            last_updated=datetime.now()
        )
        
        assert friends_list.character == character
        assert len(friends_list.friends) == 2
        assert len(friends_list) == 2
        assert isinstance(friends_list.last_updated, datetime)
        
        # Test iteration
        friend_names = [friend.name for friend in friends_list]
        assert "Jane Smith" in friend_names
        assert "Bob Johnson" in friend_names
    
    def test_search_result_creation(self):
        """Test SearchResult model creation"""
        search_result = SearchResult(
            lodestone_id=12345,
            name="John Doe",
            server="Gilgamesh",
            data_center="Aether",
            avatar_url="https://example.com/avatar.jpg"
        )
        
        assert search_result.lodestone_id == 12345
        assert search_result.name == "John Doe"
        assert search_result.server == "Gilgamesh"
        assert search_result.data_center == "Aether"
        assert search_result.avatar_url == "https://example.com/avatar.jpg"
    
    def test_search_result_to_character(self):
        """Test converting SearchResult to Character"""
        search_result = SearchResult(
            lodestone_id=12345,
            name="John Doe",
            server="Gilgamesh",
            data_center="Aether",
            avatar_url="https://example.com/avatar.jpg"
        )
        
        character = search_result.to_character()
        
        assert isinstance(character, Character)
        assert character.lodestone_id == search_result.lodestone_id
        assert character.name == search_result.name
        assert character.server == search_result.server
        assert character.data_center == search_result.data_center
        assert character.avatar_url == search_result.avatar_url

if __name__ == "__main__":
    pytest.main([__file__])
